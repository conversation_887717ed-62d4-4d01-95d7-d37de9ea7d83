import axios, { AxiosInstance } from 'axios';
import { Model, ChatMessage, OllamaResponse } from '../types';
import { EventEmitter } from 'events';

export class OllamaAPI extends EventEmitter {
  private client: AxiosInstance;
  private serverUrl: string;

  constructor(serverUrl: string) {
    super();
    this.serverUrl = serverUrl;
    this.client = axios.create({
      baseURL: serverUrl,
      timeout: 60000,
      headers: {
        'Content-Type': 'application/json',
      }
    });
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.client.get('/');
      return true;
    } catch (error) {
      return false;
    }
  }

  async listModels(): Promise<Model[]> {
    try {
      const response = await this.client.get('/api/tags');
      return response.data.models || [];
    } catch (error) {
      console.error('Error fetching models:', error);
      throw new Error('Failed to fetch models from Ollama server');
    }
  }

  async pullModel(modelName: string): Promise<void> {
    try {
      const response = await this.client.post('/api/pull', {
        name: modelName,
        stream: false
      });
      return response.data;
    } catch (error) {
      console.error('Error pulling model:', error);
      throw new Error(`Failed to pull model: ${modelName}`);
    }
  }

  async *streamChat(
    model: string,
    messages: ChatMessage[],
    options: any = {}
  ): AsyncGenerator<OllamaResponse> {
    try {
      const response = await this.client.post('/api/chat', {
        model,
        messages,
        stream: true,
        ...options
      }, {
        responseType: 'stream'
      });

      const stream = response.data;
      let buffer = '';

      for await (const chunk of stream) {
        buffer += chunk.toString();
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim()) {
            try {
              const parsed = JSON.parse(line);
              yield parsed;
            } catch (e) {
              console.error('Error parsing JSON:', e);
            }
          }
        }
      }

      if (buffer.trim()) {
        try {
          yield JSON.parse(buffer);
        } catch (e) {
          console.error('Error parsing final buffer:', e);
        }
      }
    } catch (error) {
      console.error('Error in stream chat:', error);
      throw new Error('Failed to stream chat response');
    }
  }

  async generate(
    model: string,
    prompt: string,
    options: any = {}
  ): Promise<string> {
    try {
      const response = await this.client.post('/api/generate', {
        model,
        prompt,
        stream: false,
        ...options
      });
      return response.data.response;
    } catch (error) {
      console.error('Error generating response:', error);
      throw new Error('Failed to generate response');
    }
  }

  async *streamGenerate(
    model: string,
    prompt: string,
    options: any = {}
  ): AsyncGenerator<OllamaResponse> {
    try {
      const response = await this.client.post('/api/generate', {
        model,
        prompt,
        stream: true,
        ...options
      }, {
        responseType: 'stream'
      });

      const stream = response.data;
      let buffer = '';

      for await (const chunk of stream) {
        buffer += chunk.toString();
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim()) {
            try {
              const parsed = JSON.parse(line);
              yield parsed;
            } catch (e) {
              console.error('Error parsing JSON:', e);
            }
          }
        }
      }

      if (buffer.trim()) {
        try {
          yield JSON.parse(buffer);
        } catch (e) {
          console.error('Error parsing final buffer:', e);
        }
      }
    } catch (error) {
      console.error('Error in stream generate:', error);
      throw new Error('Failed to stream generate response');
    }
  }

  async getModelInfo(modelName: string): Promise<any> {
    try {
      const response = await this.client.post('/api/show', {
        name: modelName
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching model info:', error);
      throw new Error(`Failed to get info for model: ${modelName}`);
    }
  }

  updateServerUrl(newUrl: string): void {
    this.serverUrl = newUrl;
    this.client = axios.create({
      baseURL: newUrl,
      timeout: 60000,
      headers: {
        'Content-Type': 'application/json',
      }
    });
  }

  getServerUrl(): string {
    return this.serverUrl;
  }
}