import chalk from "chalk";
import * as readline from "readline";
import { marked } from "marked";
import Terminal<PERSON>enderer from "marked-terminal";
import { ChatMessage } from "../types";
import { TaskExecution, TaskStep } from "./task-executor";

// Configure marked to use terminal renderer
marked.setOptions({
    renderer: new TerminalRenderer({
        code: chalk.yellow,
        blockquote: chalk.gray.italic,
        html: chalk.gray,
        heading: chalk.green.bold,
        firstHeading: chalk.magenta.underline.bold,
        hr: chalk.gray,
        listitem: chalk.gray,
        list: (body: string) => body,
        paragraph: chalk.white,
        table: chalk.gray,
        strong: chalk.bold,
        em: chalk.italic,
        codespan: chalk.yellow,
        del: chalk.dim.gray.strikethrough,
        link: chalk.blue,
        href: chalk.blue.underline,
    }) as any,
});

export class EnhancedInterface {
    private rl: readline.Interface;
    private escapeCount: number = 0;
    private escapeTimeout: NodeJS.Timeout | null = null;
    private isTaskExecuting: boolean = false;
    private onEscapeCallback?: () => void;
    private onDoubleEscapeCallback?: () => void;

    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            terminal: true,
            crlfDelay: Infinity,
        });

        this.setupKeyHandling();
        this.setupReadlineHandling();
    }

    private setupKeyHandling(): void {
        // Don't enable raw mode by default - only when needed
        process.stdin.resume();

        // Handle key input only when in task execution mode
        process.stdin.on("data", (key) => {
            if (!this.isTaskExecuting) {
                return; // Let readline handle normal input
            }

            const keyCode = key[0];

            // ESC key (27) - only handle during task execution
            if (keyCode === 27) {
                this.handleEscapeKey();
                return;
            }
        });
    }

    private setupReadlineHandling(): void {
        // Prevent automatic exit on close
        this.rl.on("close", () => {
            // Handle graceful shutdown
        });

        // Handle Ctrl+C
        this.rl.on("SIGINT", () => {
            if (this.isTaskExecuting) {
                this.displayWarning(
                    "Task interrupted. Press ESC to cancel or continue..."
                );
                if (this.onEscapeCallback) {
                    this.onEscapeCallback();
                }
            } else {
                this.displayInfo("Press Ctrl+C again to exit or type /exit");
            }
        });
    }

    private handleEscapeKey(): void {
        this.escapeCount++;

        if (this.escapeTimeout) {
            clearTimeout(this.escapeTimeout);
        }

        if (this.escapeCount === 1) {
            this.displayInfo(
                "ESC pressed. Press ESC again within 2 seconds to cancel task."
            );
            if (this.onEscapeCallback) {
                this.onEscapeCallback();
            }

            this.escapeTimeout = setTimeout(() => {
                this.escapeCount = 0;
            }, 2000);
        } else if (this.escapeCount === 2) {
            this.displayWarning("Double ESC detected - Cancelling task!");
            if (this.onDoubleEscapeCallback) {
                this.onDoubleEscapeCallback();
            }
            this.escapeCount = 0;
            if (this.escapeTimeout) {
                clearTimeout(this.escapeTimeout);
                this.escapeTimeout = null;
            }
        }
    }

    setTaskExecuting(executing: boolean): void {
        this.isTaskExecuting = executing;
    }

    setEscapeHandlers(
        onEscape?: () => void,
        onDoubleEscape?: () => void
    ): void {
        this.onEscapeCallback = onEscape;
        this.onDoubleEscapeCallback = onDoubleEscape;
    }

    async prompt(message: string = "> "): Promise<string> {
        return new Promise<string>((resolve) => {
            this.rl.question(chalk.cyan(message), (input: string) => {
                resolve(input.trim());
            });
        });
    }

    async promptMultiline(): Promise<string> {
        return this.prompt("\n> ");
    }

    displayMessage(message: ChatMessage): void {
        if (message.role === "user") {
            console.log(chalk.blue.bold("\n👤 You:"));
            console.log(chalk.white(message.content));
        } else if (message.role === "assistant") {
            console.log(chalk.green.bold("\n🤖 Assistant:"));
            this.displayMarkdown(message.content);
        } else if (message.role === "system") {
            console.log(chalk.yellow.bold("\n⚙️  System:"));
            console.log(chalk.gray(message.content));
        }
    }

    displayMarkdown(content: string): void {
        try {
            const rendered = marked(content);
            console.log(rendered);
        } catch (error) {
            console.log(content);
        }
    }

    // Task execution display methods
    displayTaskStarted(task: TaskExecution): void {
        console.log(
            chalk.magenta.bold("\n🚀 Task Started:"),
            chalk.white(task.title)
        );
        console.log(chalk.gray(`Task ID: ${task.id}`));
        console.log(
            chalk.gray(`Started at: ${task.startTime.toLocaleTimeString()}`)
        );
        this.displaySeparator();
    }

    displayTaskPlanning(): void {
        console.log(
            chalk.yellow.bold("\n🧠 Planning:"),
            chalk.yellow("Analyzing request and creating execution plan...")
        );
    }

    displayThinking(message: string): void {
        console.log(chalk.cyan.bold("\n💭 Thinking:"), chalk.cyan(message));
    }

    displayThinkingUpdate(chunk: string): void {
        process.stdout.write(chalk.cyan(chunk));
    }

    displayStepStarted(step: TaskStep): void {
        console.log(
            chalk.blue.bold(`\n📋 Step ${step.id}:`),
            chalk.white(step.description)
        );
        if (step.thinking) {
            console.log(chalk.gray(`   Reasoning: ${step.thinking}`));
        }
    }

    displayStepProgress(step: TaskStep, chunk: string): void {
        process.stdout.write(chalk.white(chunk));
    }

    displayStepCompleted(step: TaskStep): void {
        console.log(chalk.green.bold("\n✅ Step Completed"));
        this.displaySeparator();
    }

    displayTaskCompleted(task: TaskExecution): void {
        const duration = task.endTime
            ? (
                  (task.endTime.getTime() - task.startTime.getTime()) /
                  1000
              ).toFixed(2)
            : "Unknown";

        console.log(chalk.green.bold("\n🎉 Task Completed Successfully!"));
        console.log(chalk.gray(`Duration: ${duration} seconds`));
        console.log(
            chalk.gray(
                `Steps completed: ${
                    task.steps.filter((s) => s.status === "completed").length
                }/${task.steps.length}`
            )
        );
        this.displaySeparator();
    }

    displayTaskCancelled(task: TaskExecution): void {
        console.log(chalk.yellow.bold("\n⏹️  Task Cancelled"));
        console.log(
            chalk.gray(
                `Completed steps: ${
                    task.steps.filter((s) => s.status === "completed").length
                }/${task.steps.length}`
            )
        );
        this.displaySeparator();
    }

    displayTaskFailed(task: TaskExecution, error: any): void {
        console.log(chalk.red.bold("\n❌ Task Failed"));
        console.log(chalk.red(`Error: ${error.message || error}`));
        console.log(
            chalk.gray(
                `Completed steps: ${
                    task.steps.filter((s) => s.status === "completed").length
                }/${task.steps.length}`
            )
        );
        this.displaySeparator();
    }

    displayStatusUpdate(status: string): void {
        console.log(chalk.cyan.bold("\n📊 Status:"), chalk.cyan(status));
    }

    displayInterrupted(): void {
        console.log(chalk.yellow.bold("\n⚠️  Task Interrupted"));
        console.log(
            chalk.yellow("Press ESC again to cancel, or wait to continue...")
        );
    }

    // Standard display methods
    displayStreamingStart(): void {
        process.stdout.write(chalk.green.bold("\n🤖 Assistant: "));
    }

    displayStreamingChunk(chunk: string): void {
        process.stdout.write(chunk);
    }

    displayStreamingEnd(): void {
        console.log("\n");
    }

    clear(): void {
        console.clear();
    }

    displayError(error: string): void {
        console.log(chalk.red.bold("\n❌ Error:"), chalk.red(error));
    }

    displayWarning(warning: string): void {
        console.log(chalk.yellow.bold("\n⚠️  Warning:"), chalk.yellow(warning));
    }

    displayInfo(info: string): void {
        console.log(chalk.cyan.bold("\nℹ️  Info:"), chalk.cyan(info));
    }

    displaySuccess(message: string): void {
        console.log(chalk.green.bold("\n✅ Success:"), chalk.green(message));
    }

    displaySeparator(): void {
        console.log(chalk.gray("\n" + "─".repeat(60) + "\n"));
    }

    displayWelcome(message: string): void {
        console.log(chalk.cyan.bold(message));
    }

    displayHelp(helpText: string): void {
        console.log(chalk.white(helpText));
    }

    close(): void {
        console.log(chalk.yellow("\nGoodbye! 👋"));
        this.rl.close();
        process.exit(0);
    }

    pause(): void {
        // No-op for compatibility
    }

    resume(): void {
        // No-op for compatibility
    }
}
