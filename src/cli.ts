import { Command } from 'commander';
import chalk from 'chalk';
import ora from 'ora';
import { OllamaAPI } from './api/ollama';
import { ModelManager } from './api/models';
import { Settings } from './config/settings';
import { ChatSessionManager } from './chat/session';
import { FinalInterface } from './chat/final-interface';
import { CommandHandler } from './chat/commands';
import { WELCOME_MESSAGE } from './config/defaults';
import { ChatMessage } from './types';

export class OllamaCodeCLI {
  private program: Command;
  private settings: Settings;
  private api: OllamaAPI;
  private modelManager: ModelManager;
  private sessionManager: ChatSessionManager;
  private chatInterface: FinalInterface;
  private commandHandler: CommandHandler;
  private isRunning: boolean = false;

  constructor() {
    this.program = new Command();
    this.settings = new Settings();
    this.api = new OllamaAPI(this.settings.getServerUrl());
    this.modelManager = new ModelManager(this.api);
    this.sessionManager = new ChatSessionManager();
    this.chatInterface = new FinalInterface();
    this.commandHandler = new CommandHandler(
      this.api,
      this.modelManager,
      this.settings,
      this.sessionManager,
      this.chatInterface
    );

    this.setupCommands();
  }

  private setupCommands(): void {
    this.program
      .name('ollama-code')
      .description('Interactive Code Assistant powered by Ollama AI models')
      .version('1.0.0')
      .option('-s, --server <url>', 'Ollama server URL')
      .option('-m, --model <name>', 'Model to use')
      .option('-i, --input <text>', 'Run a single, non-interactive prompt')
      .option('--stdin', 'Read prompt from stdin for non-interactive run')
      .option('--noninteractive', 'Disable interactive TTY mode')
      .option('--list-models', 'List available models')
      .option('--pull <model>', 'Pull a model from Ollama library')
      .action(async (options) => {
        if (options.server) {
          this.api.updateServerUrl(options.server);
          this.settings.setServerUrl(options.server);
        }

        if (options.listModels) {
          await this.listModels();
          return;
        }

        if (options.pull) {
          await this.pullModel(options.pull);
          return;
        }

        if (options.model) {
          this.modelManager.setCurrentModel(options.model);
        }

        // Non-interactive modes (only if explicitly requested)
        if (options.input || options.stdin || options.noninteractive) {
          const promptText = await this.resolveNonInteractivePrompt(options);
          if (!promptText) {
            console.error(chalk.red('No input provided. Use --input or pipe text via --stdin.'));
            return;
          }
          await this.runSingleTurn(promptText);
          return;
        }

        await this.startInteractiveMode();
      });
  }

  private async resolveNonInteractivePrompt(options: any): Promise<string | null> {
    if (options.input) return options.input as string;
    if (options.stdin || !process.stdin.isTTY) {
      const chunks: Buffer[] = [];
      await new Promise<void>((resolve) => {
        process.stdin.on('data', (c) => chunks.push(Buffer.from(c)));
        process.stdin.on('end', () => resolve());
        process.stdin.resume();
      });
      const s = Buffer.concat(chunks).toString('utf8').trim();
      return s.length ? s : null;
    }
    return null;
  }

  private async ensureUsableModel(interactive: boolean): Promise<string | null> {
    let currentModel = this.modelManager.getCurrentModel();
    if (currentModel) return currentModel;

    const defaultModel = this.settings.getDefaultModel();
    if (defaultModel) {
      // Verify it exists on server
      try {
        const models = await this.modelManager.listModels();
        if (models.some(m => m.name === defaultModel)) {
          this.modelManager.setCurrentModel(defaultModel);
          return defaultModel;
        }
      } catch {}
    }

    try {
      const models = await this.modelManager.listModels();
      if (models.length >= 1) {
        // Auto-select the first model if we're in interactive mode
        if (interactive) {
          this.modelManager.setCurrentModel(models[0].name);
          return models[0].name;
        }
        // Or if there's only one model
        if (models.length === 1) {
          this.modelManager.setCurrentModel(models[0].name);
          return models[0].name;
        }
      }
      if (!interactive) {
        return null; // don't prompt in non-interactive mode
      }
    } catch {}

    return null;
  }

  private async runSingleTurn(promptText: string): Promise<void> {
    // Test connection first
    const spinner = ora('Connecting to Ollama server...').start();
    const isConnected = await this.api.testConnection();
    if (!isConnected) {
      spinner.fail('Failed to connect to Ollama server');
      console.error(chalk.red(`Cannot connect to ${this.api.getServerUrl()}`));
      return;
    } else {
      spinner.succeed('Connected');
    }

    let model = await this.ensureUsableModel(false);
    if (!model) {
      console.error(chalk.red('No model selected and multiple models available. Use --model or /models.'));
      return;
    }

    const userMessage: ChatMessage = { role: 'user', content: promptText };
    const messages = [userMessage];
    let fullResponse = '';
    try {
      for await (const chunk of this.api.streamChat(model, messages)) {
        if (chunk.message?.content) {
          process.stdout.write(chunk.message.content);
          fullResponse += chunk.message.content;
        }
      }
      process.stdout.write('\n');
    } catch (error) {
      console.error(chalk.red(`Error getting response: ${error}`));
    }
  }

  private async listModels(): Promise<void> {
    try {
      const models = await this.modelManager.listModels();
      
      if (models.length === 0) {
        console.log(chalk.yellow('No models found on the server'));
        return;
      }

      console.log(chalk.cyan.bold('\nAvailable Models:'));
      models.forEach(model => {
        console.log(this.modelManager.formatModelInfo(model));
      });
    } catch (error) {
      console.error(chalk.red('Error listing models:'), error);
    }
  }

  private async pullModel(modelName: string): Promise<void> {
    try {
      await this.modelManager.pullModel(modelName);
    } catch (error) {
      console.error(chalk.red('Error pulling model:'), error);
    }
  }

  private async startInteractiveMode(): Promise<void> {
    this.isRunning = true;
    
    // Display welcome message
    this.chatInterface.displayWelcome(WELCOME_MESSAGE);

    // Test connection
    const spinner = ora('Connecting to Ollama server...').start();
    const isConnected = await this.api.testConnection();
    
    if (!isConnected) {
      spinner.fail('Failed to connect to Ollama server');
      this.chatInterface.displayError(`Cannot connect to ${this.api.getServerUrl()}`);
      this.chatInterface.displayInfo('Use /server to change the server or check if Ollama is running');
    } else {
      spinner.succeed('Connected to Ollama server');
    }

    // Ensure a model is selected; attempt smart fallback
    let currentModel = await this.ensureUsableModel(true);
    if (!currentModel) {
      this.chatInterface.displayInfo('No model selected. Use /models to select one.');
    }

    // Create new session
    if (currentModel) {
      this.sessionManager.createNewSession(currentModel);
      this.chatInterface.displayInfo(`Using model: ${currentModel}`);
    }

    // Main chat loop
    while (this.isRunning) {
      try {
        // Multi-line input; commands are single-line starting with '/'
        const input = await this.chatInterface.promptMultiline();
        
        // Handle EOF/close
        if (input === null || input === undefined) {
          this.isRunning = false;
          break;
        }
        
        // Skip empty inputs
        if (input.trim() === '') {
          continue;
        }
        
        // Check if it's a command
        const isCommand = await this.commandHandler.handleCommand(input);
        if (isCommand) {
          continue;
        }

        // Check if a model is selected
        currentModel = this.modelManager.getCurrentModel();
        if (!currentModel) {
          this.chatInterface.displayWarning('Please select a model first using /models');
          continue;
        }

        // Add user message to session
        const userMessage: ChatMessage = {
          role: 'user',
          content: input
        };
        this.sessionManager.addMessage(userMessage);
        
        // Get conversation history
        const messages = this.sessionManager.getMessages();

        // Stream response from Ollama
        this.chatInterface.displayStreamingStart();
        let fullResponse = '';

        try {
          for await (const chunk of this.api.streamChat(currentModel, messages)) {
            if (chunk.message?.content) {
              this.chatInterface.displayStreamingChunk(chunk.message.content);
              fullResponse += chunk.message.content;
            }
          }
        } catch (error) {
          this.chatInterface.displayStreamingEnd();
          this.chatInterface.displayError(`Error getting response: ${error}`);
          continue;
        }

        this.chatInterface.displayStreamingEnd();

        // Add assistant message to session
        if (fullResponse) {
          const assistantMessage: ChatMessage = {
            role: 'assistant',
            content: fullResponse
          };
          this.sessionManager.addMessage(assistantMessage);
        }

      } catch (error: any) {
        if (error?.message?.includes('SIGINT') || 
            error?.code === 'ERR_USE_AFTER_CLOSE' || 
            error?.message?.includes('readline was closed')) {
          this.isRunning = false;
          break;
        }
        this.chatInterface.displayError(`Unexpected error: ${error}`);
        // Continue the loop on other errors
      }
    }

    this.cleanup();
  }

  private cleanup(): void {
    this.isRunning = false;
    this.chatInterface.close();
    process.exit(0);
  }

  async run(): Promise<void> {
    try {
      await this.program.parseAsync(process.argv);
    } catch (error) {
      console.error(chalk.red('Error:'), error);
      process.exit(1);
    }
  }
}
