import { OllamaConfig, ServerOption } from '../types';

// Default to local Ollama which is the most common setup
export const DEFAULT_OLLAMA_SERVER = 'http://localhost:11434';

export const DEFAULT_CONFIG: OllamaConfig = {
  serverUrl: DEFAULT_OLLAMA_SERVER,
  defaultModel: 'granite3.2-vision:latest',
  customServers: []
};

export const PREDEFINED_SERVERS: ServerOption[] = [
  {
    name: 'AI Server (Network)',
    url: 'http://ai-server:11434',
    isDefault: true
  },
  {
    name: 'Local Ollama',
    url: 'http://localhost:11434',
    isDefault: false
  },
  {
    name: 'Local (127.0.0.1)',
    url: 'http://127.0.0.1:11434',
    isDefault: false
  }
];

export const MODEL_CATEGORIES = {
  vision: ['llava', 'bakllava', 'llava-llama3', 'llava-phi3'],
  reasoning: ['mixtral', 'solar', 'qwen', 'deepseek-coder', 'wizard-math'],
  code: ['codellama', 'deepseek-coder', 'starcoder', 'wizardcoder', 'phind-codellama'],
  general: ['llama2', 'llama3', 'mistral', 'gemma', 'phi', 'neural-chat', 'starling-lm'],
  embedding: ['nomic-embed-text', 'all-minilm', 'mxbai-embed-large']
};

export const WELCOME_MESSAGE = `
Welcome to Ollama Code! 🚀
Interactive AI Assistant powered by Ollama

Type your message or use slash commands:
  /help     - Show available commands
  /models   - List and select AI models
  /server   - Change Ollama server
  /settings - Configure preferences
  /exit     - Exit the application

Default server: ${DEFAULT_OLLAMA_SERVER}
`;

export const HELP_TEXT = `
Available Commands:
  /help                - Show this help message
  /models              - List available models and select one
  /model <name>        - Quick switch to a specific model
  /server              - Change Ollama server location
  /settings            - Open settings menu
  /clear               - Clear chat history
  /save                - Save current chat session
  /load                - Load a previous chat session
  /info                - Show current model and server info
  /exit or /quit       - Exit the application

Tips:
  • Press Enter twice for multi-line input
  • Use Ctrl+C to cancel current operation
  • Models with vision support can analyze images
`;
