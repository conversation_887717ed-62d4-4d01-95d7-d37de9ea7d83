import Configstore from 'configstore';
import { OllamaConfig } from '../types';
import { DEFAULT_CONFIG } from './defaults';
import * as path from 'path';
import * as os from 'os';

export class Settings {
  private config: Configstore;
  private configPath: string;

  constructor() {
    this.config = new Configstore('ollama-code', DEFAULT_CONFIG);
    this.configPath = path.join(os.homedir(), '.ollama-code', 'config.json');
  }

  get(key?: string): any {
    if (key) {
      return this.config.get(key);
    }
    return this.config.all;
  }

  set(key: string, value: any): void {
    this.config.set(key, value);
  }

  getServerUrl(): string {
    return this.config.get('serverUrl') || DEFAULT_CONFIG.serverUrl;
  }

  setServerUrl(url: string): void {
    this.config.set('serverUrl', url);
  }

  getDefaultModel(): string | undefined {
    return this.config.get('defaultModel');
  }

  setDefaultModel(model: string): void {
    this.config.set('defaultModel', model);
  }

  getCustomServers(): string[] {
    return this.config.get('customServers') || [];
  }

  addCustomServer(url: string): void {
    const servers = this.getCustomServers();
    if (!servers.includes(url)) {
      servers.push(url);
      this.config.set('customServers', servers);
    }
  }

  removeCustomServer(url: string): void {
    const servers = this.getCustomServers();
    const index = servers.indexOf(url);
    if (index > -1) {
      servers.splice(index, 1);
      this.config.set('customServers', servers);
    }
  }

  reset(): void {
    this.config.clear();
    Object.entries(DEFAULT_CONFIG).forEach(([key, value]) => {
      this.config.set(key, value);
    });
  }

  getAll(): OllamaConfig {
    return this.config.all as OllamaConfig;
  }
}