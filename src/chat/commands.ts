import { SlashCommand } from '../types';
import { OllamaAPI } from '../api/ollama';
import { ModelManager } from '../api/models';
import { Settings } from '../config/settings';
import { ChatSessionManager } from './session';
import { FinalInterface } from './final-interface';
import { HELP_TEXT, PREDEFINED_SERVERS } from '../config/defaults';
import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';

export class CommandHandler {
  private commands: Map<string, SlashCommand> = new Map();
  private api: OllamaAPI;
  private modelManager: ModelManager;
  private settings: Settings;
  private sessionManager: ChatSessionManager;
  private chatInterface: FinalInterface;

  constructor(
    api: OllamaAPI,
    modelManager: ModelManager,
    settings: Settings,
    sessionManager: ChatSessionManager,
    chatInterface: FinalInterface
  ) {
    this.api = api;
    this.modelManager = modelManager;
    this.settings = settings;
    this.sessionManager = sessionManager;
    this.chatInterface = chatInterface;
    this.registerCommands();
  }

  private registerCommands(): void {
    // Help command
    this.commands.set('help', {
      command: 'help',
      description: 'Show available commands',
      handler: async () => {
        this.chatInterface.displayHelp(HELP_TEXT);
      }
    });

    // Models command
    this.commands.set('models', {
      command: 'models',
      description: 'List and select AI models',
      handler: async () => {
        const selectedModel = await this.modelManager.selectModel();
        if (selectedModel) {
          this.modelManager.setCurrentModel(selectedModel);
          this.sessionManager.updateModel(selectedModel);
          this.chatInterface.displaySuccess(`Switched to model: ${selectedModel}`);
        }
      }
    });

    // Model quick switch command
    this.commands.set('model', {
      command: 'model',
      description: 'Quick switch to a specific model',
      handler: async (args: string[]) => {
        if (args.length === 0) {
          this.chatInterface.displayError('Please specify a model name');
          return;
        }
        
        const modelName = args.join(' ');
        const models = await this.modelManager.listModels();
        const model = models.find(m => m.name === modelName);
        
        if (model) {
          this.modelManager.setCurrentModel(modelName);
          this.sessionManager.updateModel(modelName);
          this.chatInterface.displaySuccess(`Switched to model: ${modelName}`);
        } else {
          this.chatInterface.displayError(`Model '${modelName}' not found`);
          
          // Suggest pulling the model
          const { shouldPull } = await inquirer.prompt([
            {
              type: 'confirm',
              name: 'shouldPull',
              message: `Would you like to pull the model '${modelName}'?`,
              default: false
            }
          ]);
          
          if (shouldPull) {
            await this.modelManager.pullModel(modelName);
            this.modelManager.setCurrentModel(modelName);
            this.sessionManager.updateModel(modelName);
          }
        }
      }
    });

    // Server command
    this.commands.set('server', {
      command: 'server',
      description: 'Change Ollama server location',
      handler: async () => {
        const customServers = this.settings.getCustomServers();
        const allServers = [...PREDEFINED_SERVERS];
        
        // Add custom servers
        customServers.forEach(url => {
          allServers.push({
            name: `Custom: ${url}`,
            url,
            isDefault: false
          });
        });

        // Add option to add new server
        const choices = [
          ...allServers.map(s => ({
            name: `${s.name} (${s.url})${s.isDefault ? ' [Default]' : ''}`,
            value: s.url
          })),
          new inquirer.Separator(),
          { name: '➕ Add custom server', value: 'ADD_NEW' },
          { name: '🗑️  Remove custom server', value: 'REMOVE' }
        ];

        const { selectedServer } = await inquirer.prompt([
          {
            type: 'list',
            name: 'selectedServer',
            message: 'Select Ollama server:',
            choices
          }
        ]);

        if (selectedServer === 'ADD_NEW') {
          const { newServerUrl } = await inquirer.prompt([
            {
              type: 'input',
              name: 'newServerUrl',
              message: 'Enter custom server URL:',
              validate: (input: string) => {
                try {
                  new URL(input);
                  return true;
                } catch {
                  return 'Please enter a valid URL';
                }
              }
            }
          ]);

          // Test connection
          const spinner = ora('Testing connection...').start();
          this.api.updateServerUrl(newServerUrl);
          const isConnected = await this.api.testConnection();
          
          if (isConnected) {
            spinner.succeed('Connection successful');
            this.settings.addCustomServer(newServerUrl);
            this.settings.setServerUrl(newServerUrl);
            this.chatInterface.displaySuccess(`Server changed to: ${newServerUrl}`);
          } else {
            spinner.fail('Connection failed');
            this.chatInterface.displayError('Failed to connect to server');
            // Revert to previous server
            this.api.updateServerUrl(this.settings.getServerUrl());
          }
        } else if (selectedServer === 'REMOVE') {
          if (customServers.length === 0) {
            this.chatInterface.displayWarning('No custom servers to remove');
            return;
          }

          const { serverToRemove } = await inquirer.prompt([
            {
              type: 'list',
              name: 'serverToRemove',
              message: 'Select server to remove:',
              choices: customServers
            }
          ]);

          this.settings.removeCustomServer(serverToRemove);
          this.chatInterface.displaySuccess(`Removed server: ${serverToRemove}`);
        } else {
          // Test connection
          const spinner = ora('Testing connection...').start();
          this.api.updateServerUrl(selectedServer);
          const isConnected = await this.api.testConnection();
          
          if (isConnected) {
            spinner.succeed('Connection successful');
            this.settings.setServerUrl(selectedServer);
            this.chatInterface.displaySuccess(`Server changed to: ${selectedServer}`);
          } else {
            spinner.fail('Connection failed');
            this.chatInterface.displayError('Failed to connect to server');
            // Revert to previous server
            this.api.updateServerUrl(this.settings.getServerUrl());
          }
        }
      }
    });

    // Settings command
    this.commands.set('settings', {
      command: 'settings',
      description: 'Configure preferences',
      handler: async () => {
        const { action } = await inquirer.prompt([
          {
            type: 'list',
            name: 'action',
            message: 'Settings:',
            choices: [
              { name: 'View current settings', value: 'view' },
              { name: 'Set default model', value: 'model' },
              { name: 'Change server', value: 'server' },
              { name: 'Reset to defaults', value: 'reset' },
              { name: 'Back', value: 'back' }
            ]
          }
        ]);

        switch (action) {
          case 'view':
            const config = this.settings.getAll();
            console.log(chalk.cyan('\nCurrent Settings:'));
            console.log(chalk.white(JSON.stringify(config, null, 2)));
            break;
          
          case 'model':
            const selectedModel = await this.modelManager.selectModel();
            if (selectedModel) {
              this.settings.setDefaultModel(selectedModel);
              this.chatInterface.displaySuccess(`Default model set to: ${selectedModel}`);
            }
            break;
          
          case 'server':
            await this.commands.get('server')!.handler([]);
            break;
          
          case 'reset':
            const { confirmReset } = await inquirer.prompt([
              {
                type: 'confirm',
                name: 'confirmReset',
                message: 'Are you sure you want to reset all settings?',
                default: false
              }
            ]);
            
            if (confirmReset) {
              this.settings.reset();
              this.chatInterface.displaySuccess('Settings reset to defaults');
            }
            break;
        }
      }
    });

    // Clear command
    this.commands.set('clear', {
      command: 'clear',
      description: 'Clear chat history',
      handler: async () => {
        this.sessionManager.clearSession();
        this.chatInterface.clear();
        this.chatInterface.displayInfo('Chat history cleared');
      }
    });

    // Save command
    this.commands.set('save', {
      command: 'save',
      description: 'Save current chat session',
      handler: async () => {
        try {
          const { filename } = await inquirer.prompt([
            {
              type: 'input',
              name: 'filename',
              message: 'Enter filename for session (leave empty for auto-name):',
              filter: (input: string) => {
                if (!input) return undefined;
                return input.endsWith('.json') ? input : `${input}.json`;
              }
            }
          ]);

          const filepath = await this.sessionManager.saveSession(filename);
          this.chatInterface.displaySuccess(`Session saved to: ${filepath}`);
        } catch (error: any) {
          this.chatInterface.displayError(error.message);
        }
      }
    });

    // Load command
    this.commands.set('load', {
      command: 'load',
      description: 'Load a previous chat session',
      handler: async () => {
        try {
          const sessions = await this.sessionManager.listSessions();
          
          if (sessions.length === 0) {
            this.chatInterface.displayWarning('No saved sessions found');
            return;
          }

          const { sessionFile } = await inquirer.prompt([
            {
              type: 'list',
              name: 'sessionFile',
              message: 'Select session to load:',
              choices: sessions
            }
          ]);

          const session = await this.sessionManager.loadSession(sessionFile);
          this.modelManager.setCurrentModel(session.model);
          this.chatInterface.displaySuccess(`Loaded session: ${sessionFile}`);
          
          // Display loaded messages
          session.messages.forEach(msg => {
            this.chatInterface.displayMessage(msg);
          });
        } catch (error: any) {
          this.chatInterface.displayError(error.message);
        }
      }
    });

    // Info command
    this.commands.set('info', {
      command: 'info',
      description: 'Show current model and server info',
      handler: async () => {
        const currentModel = this.modelManager.getCurrentModel();
        const serverUrl = this.api.getServerUrl();
        const sessionInfo = this.sessionManager.getSessionInfo();

        console.log(chalk.cyan('\n📊 Current Information:'));
        console.log(chalk.white(`Server: ${serverUrl}`));
        console.log(chalk.white(`Model: ${currentModel || 'None selected'}`));
        console.log(chalk.white('\n📝 Session Info:'));
        console.log(chalk.white(sessionInfo));
      }
    });

    // Exit/Quit commands
    this.commands.set('exit', {
      command: 'exit',
      description: 'Exit the application',
      handler: async () => {
        const { shouldSave } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'shouldSave',
            message: 'Save current session before exiting?',
            default: false
          }
        ]);

        if (shouldSave) {
          await this.commands.get('save')!.handler([]);
        }

        this.chatInterface.displayInfo('Goodbye! 👋');
        process.exit(0);
      }
    });

    this.commands.set('quit', this.commands.get('exit')!);
  }

  async handleCommand(input: string): Promise<boolean> {
    if (!input.startsWith('/')) {
      return false;
    }

    const parts = input.slice(1).split(' ');
    const commandName = parts[0].toLowerCase();
    const args = parts.slice(1);

    const command = this.commands.get(commandName);
    
    if (command) {
      await command.handler(args);
      return true;
    } else {
      this.chatInterface.displayError(`Unknown command: /${commandName}. Type /help for available commands.`);
      return true;
    }
  }

  getCommands(): Map<string, SlashCommand> {
    return this.commands;
  }
}