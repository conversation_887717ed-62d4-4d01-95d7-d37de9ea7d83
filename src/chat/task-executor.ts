import chalk from 'chalk';
import { EventEmitter } from 'events';
import { ChatMessage } from '../types';
import { OllamaAPI } from '../api/ollama';

export interface TaskStep {
  id: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  thinking?: string;
  result?: string;
  error?: string;
}

export interface TaskExecution {
  id: string;
  title: string;
  description: string;
  steps: TaskStep[];
  status: 'planning' | 'executing' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  isInterrupted: boolean;
}

export class TaskExecutor extends EventEmitter {
  private api: OllamaAPI;
  private currentTask: TaskExecution | null = null;
  private isExecuting: boolean = false;
  private shouldCancel: boolean = false;

  constructor(api: OllamaAPI) {
    super();
    this.api = api;
  }

  async executeTask(userMessage: string, model: string): Promise<TaskExecution> {
    if (this.isExecuting) {
      throw new Error('Another task is already executing');
    }

    this.isExecuting = true;
    this.shouldCancel = false;

    const task: TaskExecution = {
      id: this.generateTaskId(),
      title: this.extractTaskTitle(userMessage),
      description: userMessage,
      steps: [],
      status: 'planning',
      startTime: new Date(),
      isInterrupted: false
    };

    this.currentTask = task;
    this.emit('taskStarted', task);

    try {
      // Phase 1: Planning - Let AI think about the task
      this.emit('statusUpdate', 'Planning task execution...');
      await this.planTask(task, userMessage, model);

      if (this.shouldCancel) {
        task.status = 'cancelled';
        task.isInterrupted = true;
        this.emit('taskCancelled', task);
        return task;
      }

      // Phase 2: Execution - Execute each step
      task.status = 'executing';
      this.emit('statusUpdate', 'Executing task steps...');
      
      for (const step of task.steps) {
        if (this.shouldCancel) {
          step.status = 'cancelled';
          task.status = 'cancelled';
          task.isInterrupted = true;
          this.emit('taskCancelled', task);
          return task;
        }

        await this.executeStep(step, model);
        this.emit('stepCompleted', step);
      }

      // Phase 3: Completion
      task.status = 'completed';
      task.endTime = new Date();
      this.emit('taskCompleted', task);

    } catch (error) {
      task.status = 'failed';
      task.endTime = new Date();
      this.emit('taskFailed', task, error);
    } finally {
      this.isExecuting = false;
      this.currentTask = null;
    }

    return task;
  }

  private async planTask(task: TaskExecution, userMessage: string, model: string): Promise<void> {
    const planningPrompt = `
You are an AI assistant that breaks down user requests into actionable steps. 
Analyze this request and create a step-by-step plan:

User Request: "${userMessage}"

Please think through this request and break it down into clear, actionable steps. 
For each step, provide:
1. A clear description of what needs to be done
2. Your reasoning/thinking process for that step

Format your response as:
THINKING: [Your analysis and reasoning about the overall task]

STEPS:
1. [Step description] - [Your thinking about this step]
2. [Step description] - [Your thinking about this step]
...

Be thorough but concise. Focus on actionable steps that can be executed.
`;

    this.emit('thinking', 'Analyzing the request and creating execution plan...');

    const messages: ChatMessage[] = [
      { role: 'user', content: planningPrompt }
    ];

    let fullResponse = '';
    for await (const chunk of this.api.streamChat(model, messages)) {
      if (this.shouldCancel) return;
      
      if (chunk.message?.content) {
        fullResponse += chunk.message.content;
        this.emit('thinkingUpdate', chunk.message.content);
      }
    }

    // Parse the response to extract steps
    this.parseTaskPlan(task, fullResponse);
  }

  private parseTaskPlan(task: TaskExecution, response: string): void {
    const lines = response.split('\n');
    let currentSection = '';
    let stepCounter = 1;

    for (const line of lines) {
      const trimmed = line.trim();
      
      if (trimmed.startsWith('THINKING:')) {
        currentSection = 'thinking';
        continue;
      } else if (trimmed.startsWith('STEPS:')) {
        currentSection = 'steps';
        continue;
      }

      if (currentSection === 'steps' && trimmed.match(/^\d+\./)) {
        const stepMatch = trimmed.match(/^\d+\.\s*(.+?)(?:\s*-\s*(.+))?$/);
        if (stepMatch) {
          const step: TaskStep = {
            id: `step-${stepCounter++}`,
            description: stepMatch[1].trim(),
            status: 'pending',
            thinking: stepMatch[2]?.trim()
          };
          task.steps.push(step);
        }
      }
    }

    // If no steps were parsed, create a default step
    if (task.steps.length === 0) {
      task.steps.push({
        id: 'step-1',
        description: 'Process the user request',
        status: 'pending',
        thinking: 'Execute the user\'s request as specified'
      });
    }
  }

  private async executeStep(step: TaskStep, model: string): Promise<void> {
    step.status = 'running';
    this.emit('stepStarted', step);

    const executionPrompt = `
Execute this specific step: "${step.description}"

Context: ${step.thinking || 'No additional context provided'}

Please execute this step and provide:
1. Your thinking process as you work through it
2. The result or outcome of executing this step
3. Any important observations or next considerations

Be thorough and explain your reasoning as you work.
`;

    this.emit('thinking', `Executing: ${step.description}`);

    const messages: ChatMessage[] = [
      { role: 'user', content: executionPrompt }
    ];

    let fullResponse = '';
    for await (const chunk of this.api.streamChat(model, messages)) {
      if (this.shouldCancel) {
        step.status = 'cancelled';
        return;
      }
      
      if (chunk.message?.content) {
        fullResponse += chunk.message.content;
        this.emit('stepProgress', step, chunk.message.content);
      }
    }

    step.result = fullResponse;
    step.status = 'completed';
  }

  interrupt(): void {
    this.shouldCancel = true;
    this.emit('interrupted');
  }

  cancel(): void {
    this.shouldCancel = true;
    if (this.currentTask) {
      this.currentTask.status = 'cancelled';
      this.currentTask.isInterrupted = true;
      this.currentTask.endTime = new Date();
    }
    this.emit('cancelled');
  }

  getCurrentTask(): TaskExecution | null {
    return this.currentTask;
  }

  isTaskExecuting(): boolean {
    return this.isExecuting;
  }

  private generateTaskId(): string {
    return `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private extractTaskTitle(message: string): string {
    // Extract a concise title from the user message
    const words = message.split(' ').slice(0, 8);
    return words.join(' ') + (message.split(' ').length > 8 ? '...' : '');
  }
}
