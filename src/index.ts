#!/usr/bin/env node

import { OllamaCodeCLI } from "./cli";

async function main() {
    const cli = new OllamaCodeCLI();
    await cli.run();
}

// Handle uncaught errors (log and allow CLI to handle cleanup)
process.on("uncaughtException", (error) => {
    console.error("Uncaught Exception:", error);
});

process.on("unhandledRejection", (reason, promise) => {
    console.error("Unhandled Rejection at:", promise, "reason:", reason);
});

// Let the interactive loop manage Ctrl+C and exits
process.on("SIGINT", () => {
    // noop: SimpleInterface handles SIGINT to avoid instant exit
});

process.on("SIGTERM", () => {
    console.log("Received SIGTERM");
});

// Run the CLI
main().catch((error) => {
    console.error("Fatal error:", error);
});
