# Ollama Code

An interactive CLI tool for chatting with Ollama AI models, designed as an alternative to Claude CLI that uses local or network-hosted Ollama instances.

## Features

- 🤖 **Multiple Model Support** - Switch between different Ollama models on the fly
- 🌐 **Flexible Server Configuration** - Connect to local or remote Ollama instances
- 📝 **Model Categorization** - Models organized by capabilities (Vision, Code, Reasoning, etc.)
- 💬 **Interactive Chat Interface** - Rich terminal UI with markdown support
- 📁 **Session Management** - Save and load chat sessions
- ⚙️ **Persistent Settings** - Remember your preferences between sessions
- 🎨 **Syntax Highlighting** - Beautiful code formatting in responses
- 🔧 **Slash Commands** - Quick access to common actions

## Installation

### Prerequisites

- Node.js 18+ installed
- Ollama server running (locally or on network)

### Install from source

```bash
# Clone the repository
git clone <repository-url>
cd ollama-cli

# Install dependencies
npm install

# Build the project
npm run build

# Install globally
npm link
```

## Usage

### Basic Usage

Start the interactive chat:

```bash
ollama-code
```

### Command Line Options

```bash
# Specify custom server
ollama-code --server http://localhost:11434

# Use specific model
ollama-code --model llama2

# List available models
ollama-code --list-models

# Pull a new model
ollama-code --pull codellama

# Non-interactive, single prompt
echo "Write a bash script to list files" | ollama-code --stdin --model llama3

# Or pass the prompt directly
ollama-code --input "Explain binary search in JS" --model llama3

# Force non-interactive mode (CI)
ollama-code --noninteractive --input "Summarize this repo" --model llama3
```

## Configuration

### Default Server

By default, Ollama Code connects to `http://localhost:11434`. You can change this using:

1. The `/server` command in chat
2. The `--server` flag when starting
3. The settings configuration

### Server Options

The tool provides quick access to common server configurations:

- **AI Server (Network)** - `http://ai-server:11434` (default)
- **Local Ollama** - `http://localhost:11434`
- **Custom Server** - Add your own server URLs

## Slash Commands

While in chat, use these commands:

- `/help` - Show available commands
- `/models` - List and select AI models
- `/model <name>` - Quick switch to a specific model
- `/server` - Change Ollama server location
- `/settings` - Configure preferences
- `/clear` - Clear chat history
- `/save` - Save current chat session
- `/load` - Load a previous chat session
- `/info` - Show current model and server info
- `/exit` or `/quit` - Exit the application

## Model Categories

Models are automatically categorized based on their capabilities:

### 🔵 Vision Models
- llava
- bakllava
- llava-llama3
- llava-phi3

### 💚 Code Generation Models
- codellama
- deepseek-coder
- starcoder
- wizardcoder
- phind-codellama

### 🟡 Reasoning Models
- mixtral
- solar
- qwen
- deepseek-coder
- wizard-math

### ⚪ General Purpose Models
- llama2
- llama3
- mistral
- gemma
- phi
- neural-chat
- starling-lm

### 🟣 Embedding Models
- nomic-embed-text
- all-minilm
- mxbai-embed-large

## Multi-line Input

For multi-line messages:
1. Type your first line
2. Press Enter
3. Continue typing additional lines
4. Press Enter twice to send the message

Tip: Slash commands like `/help` must be typed on the first line; they are not multi-line.

## Session Management

### Save a Session

```
/save [filename]
```

Sessions are saved to `~/.ollama-code/sessions/`

### Load a Session

```
/load
```

Select from previously saved sessions to continue a conversation.

## Configuration Files

Settings are stored in `~/.ollama-code/config.json`

Example configuration:

```json
{
  "serverUrl": "http://ai-server:11434",
  "defaultModel": "llama2",
  "customServers": [
    "http://*************:11434"
  ]
}
```

## Troubleshooting

### Cannot connect to server

1. Verify Ollama is running: `curl http://your-server:11434`
2. Check firewall settings
3. Use `/server` to change server URL
4. For local installation: ensure Ollama is started with `ollama serve`

### No models available

1. Pull models on the Ollama server: `ollama pull llama2`
2. Use `--pull` flag: `ollama-code --pull llama2`
3. Check server has models: `ollama-code --list-models`

### Model not responding

1. Check if model is fully downloaded
2. Verify server has enough resources
3. Try a smaller model variant

## Development

```bash
# Install dependencies
npm install

# Run in development mode
npm run dev

# Build the project
npm run build

# Watch for changes
npm run watch
```

## Requirements

- Node.js 18 or higher
- Ollama server (local or remote)
- Terminal with UTF-8 support for best experience

## License

MIT

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## Acknowledgments

Built as an alternative to Claude CLI, leveraging the power of Ollama for local and network-based AI model inference.
