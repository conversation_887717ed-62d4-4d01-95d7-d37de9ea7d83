{"name": "ollama-code", "version": "1.0.0", "description": "Interactive Code Assistant powered by Ollama AI models", "main": "dist/index.js", "bin": {"ollama-code": "./dist/index.js"}, "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "prestart": "npm run build", "start": "node dist/index.js", "watch": "tsc -w", "clean": "rm -rf dist", "prepare": "npm run build"}, "keywords": ["ollama", "ai", "cli", "chat", "llm"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.5", "chalk": "^4.1.2", "commander": "^11.1.0", "configstore": "^5.0.1", "inquirer": "^8.2.6", "marked": "^11.1.1", "marked-terminal": "^6.2.0", "ora": "^5.4.1", "readline-sync": "^1.4.10", "uuid": "^9.0.1"}, "devDependencies": {"@types/configstore": "^6.0.2", "@types/inquirer": "^9.0.7", "@types/marked": "^5.0.2", "@types/marked-terminal": "^3.1.6", "@types/node": "^20.10.6", "@types/readline-sync": "^1.4.8", "@types/uuid": "^9.0.7", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}