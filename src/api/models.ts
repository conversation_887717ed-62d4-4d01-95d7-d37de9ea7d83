import { Model, ModelCapabilities } from '../types';
import { OllamaAPI } from './ollama';
import { MODEL_CATEGORIES } from '../config/defaults';
import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';

export class ModelManager {
  private api: OllamaAPI;
  private currentModel: string | null = null;

  constructor(api: OllamaAPI) {
    this.api = api;
  }

  async listModels(): Promise<Model[]> {
    const spinner = ora('Fetching available models...').start();
    try {
      const models = await this.api.listModels();
      spinner.succeed('Models fetched successfully');
      return models;
    } catch (error) {
      spinner.fail('Failed to fetch models');
      throw error;
    }
  }

  categorizeModel(modelName: string): ModelCapabilities {
    const name = modelName.toLowerCase();
    
    return {
      vision: MODEL_CATEGORIES.vision.some(v => name.includes(v)),
      voice: false, // Ollama doesn't currently support voice models
      reasoning: MODEL_CATEGORIES.reasoning.some(r => name.includes(r)),
      codeGeneration: MODEL_CATEGORIES.code.some(c => name.includes(c)),
      embedding: MODEL_CATEGORIES.embedding.some(e => name.includes(e))
    };
  }

  formatModelInfo(model: Model): string {
    const capabilities = this.categorizeModel(model.name);
    const tags: string[] = [];

    if (capabilities.vision) tags.push(chalk.cyan('👁️  Vision'));
    if (capabilities.reasoning) tags.push(chalk.yellow('🧠 Reasoning'));
    if (capabilities.codeGeneration) tags.push(chalk.green('💻 Code'));
    if (capabilities.embedding) tags.push(chalk.magenta('🔤 Embedding'));

    const size = (model.size / (1024 * 1024 * 1024)).toFixed(2);
    const sizeStr = chalk.gray(`${size} GB`);

    return `${model.name} ${sizeStr} ${tags.length > 0 ? `[${tags.join(', ')}]` : ''}`;
  }

  async selectModel(): Promise<string | null> {
    const models = await this.listModels();
    
    if (models.length === 0) {
      console.log(chalk.yellow('No models found. Please pull a model first.'));
      return null;
    }

    // Group models by category
    const categorizedModels: { [key: string]: Model[] } = {
      'Vision Models': [],
      'Code Models': [],
      'Reasoning Models': [],
      'General Models': [],
      'Embedding Models': []
    };

    models.forEach(model => {
      const capabilities = this.categorizeModel(model.name);
      
      if (capabilities.vision) {
        categorizedModels['Vision Models'].push(model);
      } else if (capabilities.codeGeneration) {
        categorizedModels['Code Models'].push(model);
      } else if (capabilities.reasoning) {
        categorizedModels['Reasoning Models'].push(model);
      } else if (capabilities.embedding) {
        categorizedModels['Embedding Models'].push(model);
      } else {
        categorizedModels['General Models'].push(model);
      }
    });

    // Create choices for inquirer
    const choices: any[] = [];
    
    Object.entries(categorizedModels).forEach(([category, models]) => {
      if (models.length > 0) {
        choices.push(new inquirer.Separator(`\n${chalk.bold(category)}`));
        models.forEach(model => {
          choices.push({
            name: this.formatModelInfo(model),
            value: model.name
          });
        });
      }
    });

    if (choices.length === 0) {
      console.log(chalk.yellow('No models available'));
      return null;
    }

    const { selectedModel } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedModel',
        message: 'Select a model:',
        choices,
        pageSize: 15
      }
    ]);

    // If the model has variants, ask for specific variant
    const variants = await this.getModelVariants(selectedModel);
    if (variants.length > 1) {
      const { variant } = await inquirer.prompt([
        {
          type: 'list',
          name: 'variant',
          message: `Select a variant of ${selectedModel}:`,
          choices: variants.map(v => ({
            name: `${v.tag} (${v.size})`,
            value: v.fullName
          }))
        }
      ]);
      
      this.currentModel = variant;
      return variant;
    }

    this.currentModel = selectedModel;
    return selectedModel;
  }

  async getModelVariants(modelName: string): Promise<Array<{ tag: string; size: string; fullName: string }>> {
    try {
      const models = await this.api.listModels();
      const baseModel = modelName.split(':')[0];
      
      const variants = models
        .filter(m => m.name.startsWith(baseModel))
        .map(m => {
          const parts = m.name.split(':');
          const tag = parts[1] || 'latest';
          const size = (m.size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
          
          return {
            tag,
            size,
            fullName: m.name
          };
        });

      return variants.length > 0 ? variants : [{ tag: 'latest', size: 'Unknown', fullName: modelName }];
    } catch (error) {
      return [{ tag: 'latest', size: 'Unknown', fullName: modelName }];
    }
  }

  async pullModel(modelName: string): Promise<void> {
    const spinner = ora(`Pulling model ${modelName}...`).start();
    try {
      await this.api.pullModel(modelName);
      spinner.succeed(`Model ${modelName} pulled successfully`);
    } catch (error) {
      spinner.fail(`Failed to pull model ${modelName}`);
      throw error;
    }
  }

  getCurrentModel(): string | null {
    return this.currentModel;
  }

  setCurrentModel(model: string): void {
    this.currentModel = model;
  }

  async getModelInfo(modelName: string): Promise<any> {
    const spinner = ora(`Fetching info for ${modelName}...`).start();
    try {
      const info = await this.api.getModelInfo(modelName);
      spinner.succeed('Model info fetched');
      return info;
    } catch (error) {
      spinner.fail('Failed to fetch model info');
      throw error;
    }
  }
}