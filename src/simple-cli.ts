#!/usr/bin/env node

import chalk from 'chalk';
import ora from 'ora';
import { OllamaAPI } from './api/ollama';
import { ModelManager } from './api/models';
import { Settings } from './config/settings';
import { ChatSessionManager } from './chat/session';
import { SimpleReadlineInterface } from './chat/simple-readline';
// import { CommandHandler } from './chat/commands';
import { WELCOME_MESSAGE } from './config/defaults';
import { ChatMessage } from './types';

export class SimpleCLI {
  private settings: Settings;
  private api: OllamaAPI;
  private modelManager: ModelManager;
  private sessionManager: ChatSessionManager;
  private chatInterface: SimpleReadlineInterface;
  constructor() {
    this.settings = new Settings();
    this.api = new OllamaAPI(this.settings.getServerUrl());
    this.modelManager = new ModelManager(this.api);
    this.sessionManager = new ChatSessionManager();
    this.chatInterface = new SimpleReadlineInterface();
  }

  async start(): Promise<void> {
    // Display welcome message
    this.chatInterface.displayWelcome(WELCOME_MESSAGE);

    // Test connection
    const spinner = ora('Connecting to Ollama server...').start();
    const isConnected = await this.api.testConnection();
    
    if (!isConnected) {
      spinner.fail('Failed to connect to Ollama server');
      this.chatInterface.displayError(`Cannot connect to ${this.api.getServerUrl()}`);
      this.chatInterface.displayInfo('Use /server to change the server or check if Ollama is running');
    } else {
      spinner.succeed('Connected to Ollama server');
    }

    // Auto-select first available model
    try {
      const models = await this.modelManager.listModels();
      if (models.length > 0) {
        const firstModel = models[0].name;
        this.modelManager.setCurrentModel(firstModel);
        this.sessionManager.createNewSession(firstModel);
        this.chatInterface.displayInfo(`Using model: ${firstModel}`);
      } else {
        this.chatInterface.displayInfo('No model found. Use /models to manage models.');
      }
    } catch (error) {
      this.chatInterface.displayWarning('Could not fetch models. Use /models to select one.');
    }

    // Start the prompt loop - this is the key part that works!
    this.promptLoop();
  }

  private promptLoop(): void {
    this.chatInterface.promptLoop(async (input) => {
      try {
        // Handle empty input
        if (input === '') {
          this.promptLoop(); // Continue the loop
          return;
        }

        // Handle /exit
        if (input === '/exit' || input === '/quit') {
          this.chatInterface.close();
          process.exit(0);
          return;
        }

        // Handle basic commands
        if (input === '/help') {
          this.chatInterface.displayHelp(`
Available Commands:
  /help     - Show this help message
  /models   - List available models  
  /exit     - Exit the application
          `);
          this.promptLoop();
          return;
        }

        if (input === '/models') {
          try {
            const models = await this.modelManager.listModels();
            console.log(chalk.cyan.bold('\nAvailable Models:'));
            models.forEach(model => {
              console.log(`- ${model.name}`);
            });
          } catch (error) {
            this.chatInterface.displayError('Failed to list models');
          }
          this.promptLoop();
          return;
        }

        // Check if we have a model
        const currentModel = this.modelManager.getCurrentModel();
        if (!currentModel) {
          this.chatInterface.displayWarning('Please select a model first using /models');
          this.promptLoop();
          return;
        }

        // Handle chat message
        const userMessage: ChatMessage = {
          role: 'user',
          content: input
        };
        this.sessionManager.addMessage(userMessage);
        
        // Get conversation history
        const messages = this.sessionManager.getMessages();

        // Stream response from Ollama
        this.chatInterface.displayStreamingStart();
        let fullResponse = '';

        try {
          for await (const chunk of this.api.streamChat(currentModel, messages)) {
            if (chunk.message?.content) {
              this.chatInterface.displayStreamingChunk(chunk.message.content);
              fullResponse += chunk.message.content;
            }
          }
        } catch (error) {
          this.chatInterface.displayStreamingEnd();
          this.chatInterface.displayError(`Error getting response: ${error}`);
          this.promptLoop();
          return;
        }

        this.chatInterface.displayStreamingEnd();

        // Add assistant message to session
        if (fullResponse) {
          const assistantMessage: ChatMessage = {
            role: 'assistant',
            content: fullResponse
          };
          this.sessionManager.addMessage(assistantMessage);
        }

        // Continue the loop
        this.promptLoop();

      } catch (error) {
        this.chatInterface.displayError(`Unexpected error: ${error}`);
        this.promptLoop();
      }
    });
  }
}