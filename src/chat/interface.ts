import chalk from 'chalk';
import readline from 'readline';
import { marked } from 'marked';
import TerminalRenderer from 'marked-terminal';
import { ChatMessage } from '../types';

// Configure marked to use terminal renderer
marked.setOptions({
  renderer: new TerminalRenderer({
    code: chalk.yellow,
    blockquote: chalk.gray.italic,
    html: chalk.gray,
    heading: chalk.green.bold,
    firstHeading: chalk.magenta.underline.bold,
    hr: chalk.gray,
    listitem: chalk.gray,
    list: (body: string) => body,
    paragraph: chalk.white,
    table: chalk.gray,
    strong: chalk.bold,
    em: chalk.italic,
    codespan: chalk.yellow,
    del: chalk.dim.gray.strikethrough,
    link: chalk.blue,
    href: chalk.blue.underline,
  }) as any
});

export class ChatInterface {
  private rl: readline.Interface | null = null;

  constructor() {
    // Always initialize readline
    this.initReadline();
  }

  private initReadline(): void {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: chalk.cyan('> '),
      terminal: process.stdin.isTTY || false
    });
    
    let exitCount = 0;
    
    // Handle Ctrl+C
    this.rl.on('SIGINT', () => {
      exitCount++;
      if (exitCount === 1) {
        console.log(chalk.yellow('\n(Press Ctrl+C again to exit or type /exit)'));
        this.rl?.prompt();
      } else {
        this.close();
      }
    });
    
    // Reset exit count on any input
    this.rl.on('line', () => {
      exitCount = 0;
    });
  }

  async prompt(message: string = '> '): Promise<string> {
    // Ensure readline is initialized
    if (!this.rl) {
      this.initReadline();
    }

    return new Promise((resolve) => {
      this.rl!.question(chalk.cyan(message), (answer) => {
        resolve(answer);
      });
    });
  }

  displayMessage(message: ChatMessage): void {
    if (message.role === 'user') {
      console.log(chalk.blue.bold('\n👤 You:'));
      console.log(chalk.white(message.content));
    } else if (message.role === 'assistant') {
      console.log(chalk.green.bold('\n🤖 Assistant:'));
      this.displayMarkdown(message.content);
    } else if (message.role === 'system') {
      console.log(chalk.yellow.bold('\n⚙️  System:'));
      console.log(chalk.gray(message.content));
    }
  }

  displayMarkdown(content: string): void {
    try {
      const rendered = marked(content);
      console.log(rendered);
    } catch (error) {
      // Fallback to plain text if markdown parsing fails
      console.log(content);
    }
  }

  displayStreamingStart(): void {
    process.stdout.write(chalk.green.bold('\n🤖 Assistant: '));
  }

  displayStreamingChunk(chunk: string): void {
    process.stdout.write(chunk);
  }

  displayStreamingEnd(): void {
    console.log('\n');
  }

  clear(): void {
    console.clear();
  }

  displayError(error: string): void {
    console.log(chalk.red.bold('\n❌ Error:'), chalk.red(error));
  }

  displayWarning(warning: string): void {
    console.log(chalk.yellow.bold('\n⚠️  Warning:'), chalk.yellow(warning));
  }

  displayInfo(info: string): void {
    console.log(chalk.cyan.bold('\nℹ️  Info:'), chalk.cyan(info));
  }

  displaySuccess(message: string): void {
    console.log(chalk.green.bold('\n✅ Success:'), chalk.green(message));
  }

  displaySeparator(): void {
    console.log(chalk.gray('\n' + '─'.repeat(50) + '\n'));
  }

  displayWelcome(message: string): void {
    console.log(chalk.cyan.bold(message));
  }

  displayHelp(helpText: string): void {
    console.log(chalk.white(helpText));
  }

  close(): void {
    if (this.rl) {
      console.log(chalk.yellow('\nGoodbye! 👋'));
      this.rl.close();
      process.exit(0);
    }
  }

  pause(): void {
    this.rl?.pause();
  }

  resume(): void {
    this.rl?.resume();
  }
}