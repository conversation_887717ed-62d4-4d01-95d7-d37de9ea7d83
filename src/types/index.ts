export interface OllamaConfig {
  serverUrl: string;
  defaultModel?: string;
  customServers?: string[];
}

export interface Model {
  name: string;
  modified_at: string;
  size: number;
  digest: string;
  details?: ModelDetails;
}

export interface ModelDetails {
  format: string;
  family: string;
  families?: string[];
  parameter_size: string;
  quantization_level: string;
}

export interface ModelCapabilities {
  vision: boolean;
  voice: boolean;
  reasoning: boolean;
  codeGeneration: boolean;
  embedding: boolean;
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  images?: string[];
}

export interface ChatSession {
  id: string;
  model: string;
  messages: ChatMessage[];
  createdAt: Date;
  lastModified: Date;
}

export interface OllamaResponse {
  model: string;
  created_at: string;
  response?: string;
  message?: ChatMessage;
  done: boolean;
  context?: number[];
  total_duration?: number;
  load_duration?: number;
  prompt_eval_duration?: number;
  eval_duration?: number;
  eval_count?: number;
}

export interface ServerOption {
  name: string;
  url: string;
  isDefault: boolean;
}

export interface SlashCommand {
  command: string;
  description: string;
  handler: (args: string[]) => Promise<void>;
}