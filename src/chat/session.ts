import { ChatMessage, ChatSession } from '../types';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as os from 'os';

export class ChatSessionManager {
  private currentSession: ChatSession | null = null;
  private sessionsDir: string;

  constructor() {
    this.sessionsDir = path.join(os.homedir(), '.ollama-code', 'sessions');
    this.ensureSessionsDir();
  }

  private async ensureSessionsDir(): Promise<void> {
    try {
      await fs.mkdir(this.sessionsDir, { recursive: true });
    } catch (error) {
      console.error('Error creating sessions directory:', error);
    }
  }

  createNewSession(model: string): ChatSession {
    this.currentSession = {
      id: uuidv4(),
      model,
      messages: [],
      createdAt: new Date(),
      lastModified: new Date()
    };
    return this.currentSession;
  }

  getCurrentSession(): ChatSession | null {
    return this.currentSession;
  }

  addMessage(message: ChatMessage): void {
    if (this.currentSession) {
      this.currentSession.messages.push(message);
      this.currentSession.lastModified = new Date();
    }
  }

  getMessages(): ChatMessage[] {
    return this.currentSession?.messages || [];
  }

  clearSession(): void {
    if (this.currentSession) {
      this.currentSession.messages = [];
      this.currentSession.lastModified = new Date();
    }
  }

  async saveSession(filename?: string): Promise<string> {
    if (!this.currentSession) {
      throw new Error('No active session to save');
    }

    const sessionFilename = filename || `session-${Date.now()}.json`;
    const filepath = path.join(this.sessionsDir, sessionFilename);

    try {
      await fs.writeFile(
        filepath,
        JSON.stringify(this.currentSession, null, 2),
        'utf-8'
      );
      return filepath;
    } catch (error) {
      throw new Error(`Failed to save session: ${error}`);
    }
  }

  async loadSession(filename: string): Promise<ChatSession> {
    const filepath = path.join(this.sessionsDir, filename);

    try {
      const data = await fs.readFile(filepath, 'utf-8');
      const session = JSON.parse(data) as ChatSession;
      
      // Convert date strings back to Date objects
      session.createdAt = new Date(session.createdAt);
      session.lastModified = new Date(session.lastModified);
      
      this.currentSession = session;
      return session;
    } catch (error) {
      throw new Error(`Failed to load session: ${error}`);
    }
  }

  async listSessions(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.sessionsDir);
      return files.filter(f => f.endsWith('.json'));
    } catch (error) {
      console.error('Error listing sessions:', error);
      return [];
    }
  }

  async deleteSession(filename: string): Promise<void> {
    const filepath = path.join(this.sessionsDir, filename);
    
    try {
      await fs.unlink(filepath);
    } catch (error) {
      throw new Error(`Failed to delete session: ${error}`);
    }
  }

  updateModel(model: string): void {
    if (this.currentSession) {
      this.currentSession.model = model;
      this.currentSession.lastModified = new Date();
    }
  }

  getSessionInfo(): string {
    if (!this.currentSession) {
      return 'No active session';
    }

    const messageCount = this.currentSession.messages.length;
    const duration = Date.now() - this.currentSession.createdAt.getTime();
    const durationMinutes = Math.floor(duration / 60000);

    return `Session ID: ${this.currentSession.id}
Model: ${this.currentSession.model}
Messages: ${messageCount}
Duration: ${durationMinutes} minutes
Created: ${this.currentSession.createdAt.toLocaleString()}`;
  }
}